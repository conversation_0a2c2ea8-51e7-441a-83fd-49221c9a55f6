# GiveRound - Micro Giving Circle

A modern React application that enables users to round up their purchases and automatically donate the spare change to charities and causes they care about.

## 🚀 Features

### ✅ Implemented
- **Complete Supabase Integration** - Full database setup with authentication
- **User Authentication** - Secure signup/login with profile management
- **Real-time Dashboard** - Track donations, transactions, and impact
- **Charity Discovery** - Browse and follow verified nonprofits
- **Database Schema** - Complete tables for users, charities, transactions, donations
- **Row Level Security** - Secure data access policies
- **Sample Data Generation** - Test the app with sample transactions

### 🔄 In Progress
- **Plaid Integration** - Bank account linking for real transactions
- **Payment Processing** - Actual donation processing
- **Real-time Notifications** - Transaction and donation alerts

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **UI Components**: shadcn/ui, Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **State Management**: React Context
- **Icons**: Lucide React
- **Notifications**: Sonner

## 🏗️ Database Schema

### Tables Created:
1. **users** - User profiles and settings
2. **charity_categories** - Categories for organizing charities
3. **charities** - Charity organizations with verification
4. **user_charity_follows** - User-charity relationships
5. **transactions** - Purchase transactions and round-ups
6. **donations** - Actual donations made to charities

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm
- Supabase account (already configured)

### Installation

```bash
# Clone the repository
git clone https://github.com/deion1george/micro-giving-circle.git
cd micro-giving-circle

# Install dependencies
npm install

# Start development server
npm run dev
```

### Environment Setup
The environment variables are already configured in `.env.local`:
```env
VITE_SUPABASE_URL=https://ltmmbkckohkusutbsbsu.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here
```

## 📱 Usage

1. **Sign Up/Login** - Create an account or sign in
2. **Explore Charities** - Browse verified nonprofits by category
3. **Follow Causes** - Select charities you want to support
4. **View Dashboard** - Track your giving impact and transactions
5. **Test with Sample Data** - Use the "Add Sample Data" button to test features

## 🔧 Development

### Key Files:
- `src/lib/database.ts` - Database API functions
- `src/contexts/AuthContext.tsx` - Authentication context
- `src/pages/Dashboard.tsx` - Main dashboard with real data
- `src/pages/Charities.tsx` - Charity discovery and following
- `src/pages/Profile.tsx` - User profile management

### Database Functions:
- User profile management
- Charity CRUD operations
- Transaction tracking
- Donation processing
- Follow/unfollow charities

## 🎯 Next Steps

1. **Complete Plaid Integration** - Enable real bank connections
2. **Payment Processing** - Implement actual donation flow
3. **Mobile App** - React Native version
4. **Advanced Analytics** - Detailed impact reporting
5. **Social Features** - Sharing and community features

## 📄 License

This project is part of the Lovable platform.

## 🤝 Contributing

This is a personal project, but feedback and suggestions are welcome!

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)

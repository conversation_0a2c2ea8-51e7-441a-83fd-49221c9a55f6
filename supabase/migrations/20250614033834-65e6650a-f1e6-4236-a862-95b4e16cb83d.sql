
-- Create table for storing linked bank accounts
CREATE TABLE public.linked_accounts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  plaid_access_token TEXT NOT NULL,
  plaid_item_id TEXT NOT NULL,
  plaid_account_id TEXT NOT NULL,
  account_name TEXT,
  account_type TEXT,
  account_subtype TEXT,
  mask TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.linked_accounts ENABLE ROW LEVEL SECURITY;

-- Create policies for linked_accounts
CREATE POLICY "Users can view their own linked accounts" 
  ON public.linked_accounts 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own linked accounts" 
  ON public.linked_accounts 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own linked accounts" 
  ON public.linked_accounts 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own linked accounts" 
  ON public.linked_accounts 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Add indexes for performance
CREATE INDEX idx_linked_accounts_user_id ON public.linked_accounts(user_id);
CREATE INDEX idx_linked_accounts_plaid_item_id ON public.linked_accounts(plaid_item_id);

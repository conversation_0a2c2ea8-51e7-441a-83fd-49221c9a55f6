
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { action, ...body } = await req.json();
    console.log('Plaid operation request:', { action, body });

    const PLAID_CLIENT_ID = Deno.env.get('PLAID_CLIENT_ID');
    const PLAID_SECRET = Deno.env.get('PLAID_SECRET');
    const PLAID_ENV = 'sandbox'; // Use 'development' or 'production' for live

    console.log('Environment check:', {
      hasClientId: !!PLAID_CLIENT_ID,
      hasSecret: !!PLAID_SECRET,
      clientIdLength: PLAID_CLIENT_ID?.length,
      secretLength: PLAID_SECRET?.length,
      clientIdPreview: PLAID_CLIENT_ID?.substring(0, 8) + '...',
      secretPreview: PLAID_SECRET?.substring(0, 8) + '...',
      plaidEnv: PLAID_ENV
    });

    if (!PLAID_CLIENT_ID || !PLAID_SECRET) {
      console.error('Missing Plaid credentials:', {
        hasClientId: !!PLAID_CLIENT_ID,
        hasSecret: !!PLAID_SECRET
      });
      throw new Error('Plaid credentials not configured');
    }

    // Validate credential format
    if (PLAID_CLIENT_ID.length < 10 || PLAID_SECRET.length < 10) {
      console.error('Invalid credential format:', {
        clientIdLength: PLAID_CLIENT_ID.length,
        secretLength: PLAID_SECRET.length
      });
      throw new Error('Invalid Plaid credential format');
    }

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get the user from the Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      throw new Error('Authentication failed');
    }

    switch (action) {
      case 'create_link_token': {
        console.log('Creating link token for user:', user.id);
        
        const linkTokenRequest = {
          client_id: PLAID_CLIENT_ID,
          secret: PLAID_SECRET,
          client_name: 'GiveRound',
          country_codes: ['US'],
          language: 'en',
          user: {
            client_user_id: user.id,
          },
          products: ['transactions'],
          account_filters: {
            depository: {
              account_subtypes: ['checking', 'savings'],
            },
            credit: {
              account_subtypes: ['credit card'],
            },
          },
        };

        console.log('Link token request payload:', {
          ...linkTokenRequest,
          client_id: PLAID_CLIENT_ID.substring(0, 8) + '...',
          secret: PLAID_SECRET.substring(0, 8) + '...'
        });

        const response = await fetch(`https://${PLAID_ENV}.plaid.com/link/token/create`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(linkTokenRequest),
        });

        const data = await response.json();
        console.log('Link token response:', data);

        if (!response.ok) {
          console.error('Plaid API error details:', {
            status: response.status,
            statusText: response.statusText,
            data
          });
          throw new Error(`Plaid API error (${response.status}): ${data.error_message || data.error_code || 'Unknown error'}`);
        }

        return new Response(JSON.stringify({ link_token: data.link_token }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      case 'exchange_public_token': {
        const { public_token } = body;
        console.log('Exchanging public token for user:', user.id);

        const exchangeRequest = {
          client_id: PLAID_CLIENT_ID,
          secret: PLAID_SECRET,
          public_token,
        };

        const response = await fetch(`https://${PLAID_ENV}.plaid.com/item/public_token/exchange`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(exchangeRequest),
        });

        const data = await response.json();
        console.log('Token exchange response:', data);

        if (!response.ok) {
          throw new Error(`Plaid API error: ${data.error_message || 'Unknown error'}`);
        }

        const { access_token, item_id } = data;

        // Get account information
        const accountsRequest = {
          client_id: PLAID_CLIENT_ID,
          secret: PLAID_SECRET,
          access_token,
        };

        const accountsResponse = await fetch(`https://${PLAID_ENV}.plaid.com/accounts/get`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(accountsRequest),
        });

        const accountsData = await accountsResponse.json();
        console.log('Accounts data:', accountsData);

        if (!accountsResponse.ok) {
          throw new Error(`Plaid API error: ${accountsData.error_message || 'Unknown error'}`);
        }

        // Store linked accounts in database
        const accountInserts = accountsData.accounts.map((account: any) => ({
          user_id: user.id,
          plaid_access_token: access_token,
          plaid_item_id: item_id,
          plaid_account_id: account.account_id,
          account_name: account.name,
          account_type: account.type,
          account_subtype: account.subtype,
          mask: account.mask,
        }));

        const { error: insertError } = await supabaseClient
          .from('linked_accounts')
          .insert(accountInserts);

        if (insertError) {
          console.error('Database insert error:', insertError);
          throw new Error('Failed to save account information');
        }

        return new Response(JSON.stringify({ 
          success: true, 
          accounts: accountsData.accounts.length 
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      default:
        throw new Error(`Unknown action: ${action}`);
    }
  } catch (error) {
    console.error('Plaid operation error:', error);
    return new Response(JSON.stringify({ 
      error: error.message || 'Internal server error' 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});

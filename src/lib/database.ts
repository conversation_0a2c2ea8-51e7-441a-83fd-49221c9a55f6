import { supabase } from '@/integrations/supabase/client';

// Types for our database entities
export interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  monthly_goal: number;
  total_donated: number;
  created_at: string;
  updated_at: string;
}

export interface Charity {
  id: number;
  name: string;
  description?: string;
  category_id?: number;
  impact_statement?: string;
  verified: boolean;
  rating: number;
  total_donors: number;
  total_raised: number;
  website_url?: string;
  logo_url?: string;
  ein?: string;
  created_at: string;
  updated_at: string;
  category_name?: string;
  category_icon?: string;
}

export interface Transaction {
  id: number;
  user_id: string;
  vendor_name: string;
  purchase_amount: number;
  round_up_amount: number;
  transaction_date: string;
  card_last_four?: string;
  processed: boolean;
  created_at: string;
}

export interface Donation {
  id: number;
  user_id: string;
  charity_id: number;
  amount: number;
  transaction_ids?: number[];
  status: string;
  donation_date: string;
  created_at: string;
}

export interface UserCharityFollow {
  id: number;
  user_id: string;
  charity_id: number;
  allocation_percentage: number;
  created_at: string;
}

// User functions
export const createUserProfile = async (user: {
  id: string;
  email: string;
  full_name?: string;
}) => {
  const { data, error } = await supabase
    .from('users')
    .insert([user])
    .select()
    .single();
  
  return { data, error };
};

export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single();
  
  return { data, error };
};

export const updateUserProfile = async (userId: string, updates: Partial<User>) => {
  const { data, error } = await supabase
    .from('users')
    .update(updates)
    .eq('id', userId)
    .select()
    .single();
  
  return { data, error };
};

// Charity functions
export const getCharities = async () => {
  // First try the view
  const { data, error } = await supabase
    .from('charities_with_categories')
    .select('*')
    .order('verified', { ascending: false })
    .order('rating', { ascending: false });

  if (data && !error) {
    return { data, error };
  }

  console.warn('charities_with_categories view failed, using basic charities table:', error);

  // Fallback to basic charities table with join
  const { data: charitiesData, error: charitiesError } = await supabase
    .from('charities')
    .select(`
      *,
      charity_categories(
        name,
        icon
      )
    `)
    .order('verified', { ascending: false })
    .order('rating', { ascending: false });

  if (charitiesError) {
    return { data: null, error: charitiesError };
  }

  // Transform the data to match the expected format
  const transformedData = charitiesData?.map(charity => ({
    ...charity,
    category_name: charity.charity_categories?.name,
    category_icon: charity.charity_categories?.icon
  }));

  return { data: transformedData, error: null };
};

export const getCharitiesByCategory = async (category: string) => {
  const { data, error } = await supabase
    .from('charities_with_categories')
    .select('*')
    .eq('category_name', category)
    .order('rating', { ascending: false });
  
  return { data, error };
};

export const getCharityById = async (charityId: number) => {
  const { data, error } = await supabase
    .from('charities_with_categories')
    .select('*')
    .eq('id', charityId)
    .single();
  
  return { data, error };
};

// User charity follows
export const getUserCharityFollows = async (userId: string) => {
  const { data, error } = await supabase
    .from('user_charity_follows')
    .select(`
      *,
      charities:charity_id (
        id,
        name,
        description,
        verified,
        rating,
        total_raised
      )
    `)
    .eq('user_id', userId);
  
  return { data, error };
};

export const followCharity = async (userId: string, charityId: number, allocationPercentage = 0) => {
  const { data, error } = await supabase
    .from('user_charity_follows')
    .insert([{
      user_id: userId,
      charity_id: charityId,
      allocation_percentage: allocationPercentage
    }])
    .select()
    .single();
  
  return { data, error };
};

export const unfollowCharity = async (userId: string, charityId: number) => {
  const { data, error } = await supabase
    .from('user_charity_follows')
    .delete()
    .eq('user_id', userId)
    .eq('charity_id', charityId);
  
  return { data, error };
};

export const updateCharityAllocation = async (
  userId: string, 
  charityId: number, 
  allocationPercentage: number
) => {
  const { data, error } = await supabase
    .from('user_charity_follows')
    .update({ allocation_percentage: allocationPercentage })
    .eq('user_id', userId)
    .eq('charity_id', charityId)
    .select()
    .single();
  
  return { data, error };
};

// Transaction functions
export const createTransaction = async (transaction: Omit<Transaction, 'id' | 'created_at'>) => {
  const { data, error } = await supabase
    .from('transactions')
    .insert([transaction])
    .select()
    .single();
  
  return { data, error };
};

export const getUserTransactions = async (userId: string, limit = 10) => {
  const { data, error } = await supabase
    .from('transactions')
    .select('*')
    .eq('user_id', userId)
    .order('transaction_date', { ascending: false })
    .limit(limit);
  
  return { data, error };
};

// Donation functions
export const createDonation = async (donation: Omit<Donation, 'id' | 'created_at'>) => {
  const { data, error } = await supabase
    .from('donations')
    .insert([donation])
    .select()
    .single();
  
  return { data, error };
};

export const getUserDonations = async (userId: string) => {
  const { data, error } = await supabase
    .from('donations')
    .select(`
      *,
      charities:charity_id (
        id,
        name,
        verified
      )
    `)
    .eq('user_id', userId)
    .order('donation_date', { ascending: false });
  
  return { data, error };
};

// Dashboard summary
export const getUserDashboardSummary = async (userId: string) => {
  console.log('Getting dashboard summary for user:', userId);

  if (!userId) {
    console.error('No user ID provided');
    return { data: null, error: new Error('No user ID provided') };
  }

  // Always use fallback approach for now to avoid RPC issues
  try {
    console.log('Fetching user data...');
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Failed to fetch user:', userError);
      console.error('Error details:', JSON.stringify(userError, null, 2));
      return { data: null, error: userError };
    }

    console.log('User data fetched successfully:', user);

    const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString();
    console.log('Start of month:', startOfMonth);

    const { data: donations, error: donationsError } = await supabase
      .from('donations')
      .select('amount, donation_date')
      .eq('user_id', userId)
      .gte('donation_date', startOfMonth);

    if (donationsError) {
      console.warn('Donations query failed:', donationsError);
    }

    const { data: transactions, error: transactionsError } = await supabase
      .from('transactions')
      .select('round_up_amount, transaction_date')
      .eq('user_id', userId)
      .gte('transaction_date', startOfMonth);

    if (transactionsError) {
      console.warn('Transactions query failed:', transactionsError);
    }

    const { data: charityFollows, error: charityFollowsError } = await supabase
      .from('user_charity_follows')
      .select('charity_id')
      .eq('user_id', userId);

    if (charityFollowsError) {
      console.warn('Charity follows query failed:', charityFollowsError);
    }

    const totalDonatedThisMonth = donations?.reduce((sum, d) => sum + (parseFloat(d.amount) || 0), 0) || 0;
    const totalRoundUpsThisMonth = transactions?.reduce((sum, t) => sum + (parseFloat(t.round_up_amount) || 0), 0) || 0;
    const charitiesSupported = charityFollows?.length || 0;

    const result = {
      user_id: userId,
      full_name: user.full_name || '',
      monthly_goal: parseFloat(user.monthly_goal) || 50,
      total_donated_this_month: totalDonatedThisMonth,
      total_round_ups_this_month: totalRoundUpsThisMonth,
      charities_supported: charitiesSupported
    };

    console.log('Dashboard summary result:', result);

    return {
      data: result,
      error: null
    };
  } catch (fallbackError) {
    console.error('Dashboard summary failed completely:', fallbackError);
    return {
      data: null,
      error: fallbackError
    };
  }
};

// Charity categories
export const getCharityCategories = async () => {
  const { data, error } = await supabase
    .from('charity_categories')
    .select('*')
    .order('name');
  
  return { data, error };
};

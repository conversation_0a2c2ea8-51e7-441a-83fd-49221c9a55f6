import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Check, Users, Bell, TrendingUp, Heart, Shield, Zap, Globe, Star, ArrowDown } from "lucide-react";

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="w-full py-6 px-6 bg-white">
        <div className="container mx-auto flex justify-between items-center">
          <div className="text-2xl font-bold text-foreground">
            GiveRound
          </div>
          <div className="flex gap-4">
            <Link to="/login">
              <Button variant="ghost" className="text-muted-foreground hover:text-foreground">
                Log In
              </Button>
            </Link>
            <Link to="/signup">
              <Button className="bg-emerald-600 hover:bg-emerald-700 text-white px-6">
                Get Started
              </Button>
            </Link>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-6 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto max-w-4xl text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-8 leading-tight">
            <span className="text-slate-800">Turn </span>
            <span className="bg-gradient-to-r from-emerald-600 to-green-500 bg-clip-text text-transparent">
              Spare Change Into Real Change
            </span>
          </h1>
          <p className="text-xl text-slate-600 mb-12 max-w-2xl mx-auto leading-relaxed">
            Round up your everyday purchases and automatically donate the spare 
            change to nonprofits, churches, and causes you care about.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-20">
            <Link to="/signup">
              <Button size="lg" className="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3">
                Start Giving Today
              </Button>
            </Link>
            <Button variant="outline" size="lg" className="px-8 py-3">
              Watch Demo
            </Button>
          </div>

          {/* Demo Card */}
          <div className="max-w-2xl mx-auto">
            <Card className="bg-white shadow-xl border-0 overflow-hidden">
              <CardContent className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
                  <div className="text-left">
                    <div className="text-sm text-slate-500 mb-2">Coffee Purchase</div>
                    <div className="text-3xl font-bold text-slate-800 mb-2">$4.25</div>
                    <div className="text-emerald-600 font-medium">+$0.75 rounded up</div>
                  </div>
                  
                  <div className="flex justify-center">
                    <ArrowDown className="text-slate-400" size={24} />
                  </div>
                  
                  <div className="text-left md:text-right">
                    <div className="text-sm text-slate-500 mb-2">Donated This Week</div>
                    <div className="text-3xl font-bold text-emerald-600 mb-2">$12.50</div>
                    <div className="text-slate-500 text-sm">To Local Food Bank</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 px-6 bg-white">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-slate-800 mb-4">
              How GiveRound Works
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            <div className="text-center">
              <div className="w-16 h-16 bg-slate-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Check className="text-slate-600" size={24} />
              </div>
              <h3 className="text-xl font-bold text-slate-800 mb-4">Link Your Cards</h3>
              <p className="text-slate-600 leading-relaxed">
                Securely connect your debit and credit cards. We use bank-level encryption to keep your data safe.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-slate-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <ArrowDown className="text-emerald-600" size={24} />
              </div>
              <h3 className="text-xl font-bold text-slate-800 mb-4">Round Up Purchases</h3>
              <p className="text-slate-600 leading-relaxed">
                Every purchase automatically rounds up to the nearest dollar. Your spare change adds up fast!
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-slate-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Users className="text-blue-600" size={24} />
              </div>
              <h3 className="text-xl font-bold text-slate-800 mb-4">Support Causes</h3>
              <p className="text-slate-600 leading-relaxed">
                Choose from thousands of verified nonprofits and churches. See your impact with real-time updates.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 px-6 bg-gray-50">
        <div className="container mx-auto max-w-6xl">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-slate-800 mb-2">$2.3M+</div>
              <div className="text-slate-600">Donated</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-emerald-600 mb-2">50K+</div>
              <div className="text-slate-600">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">1,200+</div>
              <div className="text-slate-600">Partner Nonprofits</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-600 mb-2">98%</div>
              <div className="text-slate-600">User Satisfaction</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-6 bg-white">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-slate-800 mb-6">
              Why Choose GiveRound?
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              The easiest way to make a difference with your everyday spending
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="pb-4">
                <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mb-4">
                  <Zap className="text-emerald-600" size={24} />
                </div>
                <CardTitle className="text-xl text-slate-800">Instant Setup</CardTitle>
                <CardDescription className="text-slate-600">
                  Connect your cards in under 60 seconds and start making an impact immediately.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="pb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <Shield className="text-blue-600" size={24} />
                </div>
                <CardTitle className="text-xl text-slate-800">Bank-Level Security</CardTitle>
                <CardDescription className="text-slate-600">
                  Your financial data is protected with the same encryption used by major banks.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="pb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                  <TrendingUp className="text-purple-600" size={24} />
                </div>
                <CardTitle className="text-xl text-slate-800">Track Your Impact</CardTitle>
                <CardDescription className="text-slate-600">
                  See exactly how your donations are making a difference with detailed impact reports.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6 bg-emerald-600">
        <div className="container mx-auto max-w-4xl text-center text-white">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Make a Difference?</h2>
          <p className="text-xl mb-10 opacity-90 max-w-2xl mx-auto">
            Join thousands of people already making an impact with their spare change.
          </p>
          <Link to="/signup">
            <Button size="lg" variant="secondary" className="bg-white text-emerald-600 hover:bg-gray-50 px-8 py-3">
              Start Your Journey Today
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 px-6 bg-slate-900 text-white">
        <div className="container mx-auto max-w-6xl">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div>
              <div className="font-bold text-xl mb-4">
                GiveRound
              </div>
              <div className="text-sm text-slate-400 leading-relaxed">
                Making charitable giving effortless through everyday purchases.
              </div>
            </div>
            <div>
              <div className="font-semibold mb-4">Product</div>
              <div className="space-y-3 text-sm text-slate-400">
                <div className="hover:text-white cursor-pointer transition-colors">How it Works</div>
                <div className="hover:text-white cursor-pointer transition-colors">Security</div>
                <div className="hover:text-white cursor-pointer transition-colors">Pricing</div>
              </div>
            </div>
            <div>
              <div className="font-semibold mb-4">Support</div>
              <div className="space-y-3 text-sm text-slate-400">
                <div className="hover:text-white cursor-pointer transition-colors">Help Center</div>
                <div className="hover:text-white cursor-pointer transition-colors">Contact Us</div>
                <div className="hover:text-white cursor-pointer transition-colors">Privacy Policy</div>
              </div>
            </div>
            <div>
              <div className="font-semibold mb-4">Connect</div>
              <div className="space-y-3 text-sm text-slate-400">
                <div className="hover:text-white cursor-pointer transition-colors">Blog</div>
                <div className="hover:text-white cursor-pointer transition-colors">Newsletter</div>
                <div className="hover:text-white cursor-pointer transition-colors">Social Media</div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;

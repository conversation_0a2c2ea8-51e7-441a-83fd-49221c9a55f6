import { useState } from 'react';
import { PlaidLink } from '@/components/PlaidLink';
import { LinkedAccounts } from '@/components/LinkedAccounts';
import { useAuth } from '@/contexts/AuthContext';

const Accounts = () => {
  const [refreshKey, setRefreshKey] = useState(0);
  const { session, user, loading } = useAuth();

  // Debug logging
  console.log('Accounts page - Auth state:', {
    session: !!session,
    user: !!user,
    loading,
    sessionDetails: session ? {
      access_token: session.access_token ? 'present' : 'missing',
      expires_at: session.expires_at,
      user_id: session.user?.id
    } : null
  });

  const handleLinkSuccess = () => {
    // Trigger a refresh of linked accounts
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="container mx-auto max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Account Management</h1>
          <p className="text-muted-foreground">
            Link your debit cards, credit cards, or bank accounts to automatically round up purchases and donate to charity
          </p>

          {/* Debug Info */}
          <div className="mt-4 p-4 bg-gray-100 rounded-lg text-sm">
            <h3 className="font-semibold mb-2">Debug Info:</h3>
            <p>Session: {session ? '✅ Active' : '❌ Missing'}</p>
            <p>User: {user ? '✅ Logged in' : '❌ Not logged in'}</p>
            <p>Loading: {loading ? '⏳ Loading...' : '✅ Ready'}</p>
            {session && (
              <p>Access Token: {session.access_token ? '✅ Present' : '❌ Missing'}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Link Account Section */}
          <PlaidLink onSuccess={handleLinkSuccess} />

          {/* Linked Accounts Section */}
          <LinkedAccounts refreshKey={refreshKey} />
        </div>
      </div>
    </div>
  );
};

export default Accounts;

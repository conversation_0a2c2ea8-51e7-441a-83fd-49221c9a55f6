import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://ltmmbkckohkusutbsbsu.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0bW1ia2Nrb2hrdXN1dGJzYnN1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkzNjQzOTYsImV4cCI6MjA2NDk0MDM5Nn0._L2_jT-3A0pCNWuZbcSvNsx_pOp9644MrlI4pTqYpCA";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CreditCard, Trash2, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface LinkedAccount {
  id: string;
  account_name: string;
  account_type: string;
  account_subtype: string;
  mask: string;
  is_active: boolean;
  created_at: string;
}

interface LinkedAccountsProps {
  refreshKey?: number;
}

export const LinkedAccounts = ({ refreshKey }: LinkedAccountsProps) => {
  const [accounts, setAccounts] = useState<LinkedAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  const fetchLinkedAccounts = async () => {
    if (!user) {
      console.log('No user found, skipping fetch');
      return;
    }

    console.log('Fetching linked accounts for user:', user.id);
    console.log('Full user object:', user);

    try {
      // First, let's see all records without filtering by user_id
      const { data: allData, error: allError } = await supabase
        .from('linked_accounts')
        .select('*');

      console.log('All linked accounts in database:', { allData, allError });

      const { data, error } = await supabase
        .from('linked_accounts')
        .select('id, account_name, account_type, account_subtype, mask, is_active, created_at, user_id')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      console.log('Filtered linked accounts query result:', { data, error });
      console.log('Query was looking for user_id:', user.id);

      if (error) {
        throw error;
      }

      console.log('Setting accounts:', data);
      setAccounts(data || []);
    } catch (error) {
      console.error('Error fetching linked accounts:', error);
      toast({
        title: "Error loading accounts",
        description: "Failed to load your linked accounts.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveAccount = async (accountId: string) => {
    try {
      const { error } = await supabase
        .from('linked_accounts')
        .update({ is_active: false })
        .eq('id', accountId);

      if (error) {
        throw error;
      }

      setAccounts(accounts.filter(account => account.id !== accountId));
      toast({
        title: "Account removed",
        description: "The account has been successfully unlinked.",
      });
    } catch (error) {
      console.error('Error removing account:', error);
      toast({
        title: "Error removing account",
        description: "Failed to remove the account. Please try again.",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchLinkedAccounts();
  }, [user, refreshKey]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Linked Accounts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">Loading accounts...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Linked Accounts</CardTitle>
            <CardDescription>
              Manage your connected debit cards, credit cards, and bank accounts
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              console.log('Manual refresh triggered');
              setLoading(true);
              fetchLinkedAccounts();
            }}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {accounts.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            No accounts linked yet. Link an account to start rounding up your purchases.
          </div>
        ) : (
          <div className="space-y-3">
            {accounts.map((account) => (
              <div
                key={account.id}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <CreditCard className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">
                      {account.account_name || `${account.account_type} Account`}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      ••••{account.mask} • {account.account_subtype}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Active
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveAccount(account.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
